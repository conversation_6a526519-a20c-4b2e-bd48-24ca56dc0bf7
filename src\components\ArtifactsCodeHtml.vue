<template>
  <ResizablePanel
    v-model:visible="visible"
    v-model:full-visible="fullVisible"
    v-model:active-tab="tab"
    :tabs="panelTabs"
    :is-share-page="isSharePage"
    :delay-content-on-fullscreen="true"
    @tab-click="(key: string) => handleTabClick(key as 'code' | 'preview')"
  >
    <template #header-actions-left>
      <button
        v-if="isPc && tab === 'preview' && false"
        class="drwan_btn"
        :title="t('artifactsCodeHtml.screenshot')"
        @click="handleScreenshot"
      >
        <i class="i-ri-screenshot-2-line text-16px"></i>
      </button>

      <button
        v-if="tab === 'preview'"
        class="drwan_btn"
        :class="{ 'bg-green-100 border-green-400': elementPickerActive, '!border-green-400': elementPickerActive }"
        :title="t('artifactsCodeHtml.elementPicker')"
        @click="toggleElementPicker"
      >
        <i
          class="i-mdi-cursor-default-click-outline text-18px"
          :class="elementPickerActive ? 'text-green-500' : ''"
        >
        </i>
      </button>

      <button
        v-if="tab === 'preview'"
        class="drwan_btn"
        :class="{ 'bg-blue-100 border-blue-400': isEditMode, '!border-blue-400': isEditMode }"
        :title="isEditMode ? t('artifactsCodeHtml.exitEditMode') : t('artifactsCodeHtml.enterEditMode')"
        @click="toggleEditMode"
      >
        <i
          class="i-ri-edit-2-line text-18px"
          :class="isEditMode ? 'text-blue-500' : ''"
        >
        </i>
      </button>

      <button
        v-if="tab === 'preview' && isEditMode"
        class="drwan_btn"
        :title="t('artifactsCodeHtml.saveAndGetHtml')"
        @click="handleSaveAndGetHtml"
      >
        <i class="i-ri-save-line text-18px"></i>
      </button>

      <button
        v-if="tab === 'code' && !isPc"
        class="drwan_btn"
        :title="t('artifactsCodeHtml.download')"
        @click="handleDownloadCode"
      >
        <i class="i-ju-download cursor-pointer"></i>
      </button>
    </template>

    <template #tab-indicator="{ isActive }">
      <Loading
        v-if="(loading || !editorContent || !iframeLoaded) && isActive && !deployLoading"
        class="mb-3px mr-8px"
      />
    </template>

    <template #content>
      <div
        v-if="tab === 'code' && isPc"
        class="flex items-center gap-15px p-20px text-16px text-#CBD5E1 lt-md:hidden"
        :class="{ 'mt-6': isClient }"
      >
        <i class="i-ju-copy2 cursor-pointer" title="Copy Code" @click="handleCopyCode"></i>
        <i class="i-ju-download cursor-pointer" title="Download Code" @click="handleDownloadCode"></i>
        <i
          v-if="!llmGenerating"
          class="text-19px"
          :class="{
            'i-ri-save-3-line cursor-pointer': !codeSaving,
            'opacity-50 pointer-events-none i-line-md-loading-twotone-loop': codeSaving,
          }"
          title="Save Code"
          @click="handleSaveCode"
        ></i>
      </div>
      <div
        v-show="tab === 'code'"
        class="flex-1 whitespace-pre px-20px pb-40px text-12px lt-md:(p-0px pb-20px)"
      >
        <CodeEditor
          ref="monacoRef"
          v-model="editorContent"
          :streaming="llmStreaming"
          :options="monacoOptions"
        />
      </div>

      <!-- 预览视图 -->
      <div v-show="tab === 'preview'" class="relative h-full w-full">
        <iframe
          ref="previewFrame"
          class="h-full w-full border-0"
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads"
          title="代码预览"
          @load="handleIframeLoad"
        ></iframe>

        <!-- 元素高亮 -->
        <div
          v-if="(elementPickerActive || elementLocked) && elementBounding && iframeLoaded"
          class="pointer-events-none fixed z-9999 border-2 bg-green-100/20 transition-all duration-100"
          :class="elementLocked ? 'border-blue-500' : 'border-green-500'"
          :style="{
            width: `${elementBounding.width}px`,
            height: `${elementBounding.height}px`,
            left: `${elementBounding.left}px`,
            top: `${elementBounding.top}px`,
            display: currentElement ? 'block' : 'none',
          }"
        ></div>

        <!-- 鼠标指针指示器 -->
        <div
          v-if="elementPickerActive && !elementLocked && iframeLoaded"
          class="pointer-events-none fixed z-9999 size-4 transform rounded-full bg-green-500 shadow-md -translate-1/2"
          :style="{
            left: `${mouseX}px`,
            top: `${mouseY}px`,
          }"
        ></div>

        <!-- 元素信息提示 -->
        <div
          v-if="elementPickerActive && !elementLocked && currentElement && elementBounding && iframeLoaded"
          class="pointer-events-none fixed z-9999 max-w-xs border border-gray-200 rounded bg-white px-3 py-2 text-xs shadow-lg"
          :style="{
            left: `${elementBounding.left}px`,
            top: `${elementBounding.top + elementBounding.height + 8}px`,
          }"
        >
          <div class="text-green-600 font-bold">{{ currentElementTag }}</div>
          <div class="truncate text-gray-600">{{ currentElementClasses }}</div>
        </div>

        <!-- 元素锁定面板 -->
        <div
          v-if="elementLocked && currentElement && elementBounding && iframeLoaded"
          class="element-locked-panel fixed z-9999 w-300px overflow-hidden rounded-md bg-[var(--white)] shadow-lg"
          :style="lockedPanelStyle"
        >
          <div class="px-10px py-3">
            <div class="flex items-center justify-between">
              <div class="text-16px text-[var(--black)] font-medium">{{ $t('artifactsCodeHtml.lockedElement') }}</div>
              <div
                class="cursor-pointer text-gray-500 hover:text-gray-700"
                @click="unlockElement"
              >
                <i class="i-ri-close-line text-16px"></i>
              </div>
            </div>
          </div>
          <div class="p-10px pt-0">
            <div class="rounded-md bg-gray-100 p-2 dark:bg-#272727">
              <textarea
                v-model="userInput"
                class="min-h-100px w-full resize-none border-none bg-transparent p-1 text-sm focus:outline-none"
                :placeholder="$t('artifactsCodeHtml.elementDescriptionPlaceholder')"
              ></textarea>
              <div class="flex items-center justify-end">
                <el-button
                  :disabled="userInput.trim() === ''"
                  circle
                  :color="isDark ? '#fff' : '#000'"
                  size="small"
                  class="send_btn"
                  @click="handleSendElement"
                >
                  <i class="i-ju-sendbox-button-arrow text-10px"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 图片编辑弹窗 -->
        <div
          v-if="imageEditVisible && currentImageElement && iframeLoaded"
          class="image-edit-modal fixed inset-0 z-10000 flex items-center justify-center bg-black/50"
          @click.self="closeImageEdit"
        >
          <div class="max-h-600px w-500px overflow-hidden rounded-md bg-[var(--white)] shadow-lg">
            <div class="px-20px py-15px">
              <div class="flex items-center justify-between">
                <div class="text-16px text-[var(--black)] font-medium">{{ $t('artifactsCodeHtml.imageEditTitle') }}</div>
                <div
                  class="cursor-pointer text-gray-500 hover:text-gray-700"
                  @click="closeImageEdit"
                >
                  <i class="i-ri-close-line text-16px"></i>
                </div>
              </div>
            </div>

            <!-- Tab 切换 -->
            <div class="px-20px">
              <div class="flex border-b border-gray-200">
                <button
                  v-for="tab in imageEditTabs"
                  :key="tab.key"
                  class="px-16px py-8px text-sm font-medium transition-colors"
                  :class="{
                    'border-b-2 border-blue-500 text-blue-600': activeImageEditTab === tab.key,
                    'text-gray-500 hover:text-gray-700': activeImageEditTab !== tab.key,
                  }"
                  @click="activeImageEditTab = tab.key"
                >
                  {{ tab.label }}
                </button>
              </div>
            </div>

            <div class="px-20px pb-20px pt-15px">
              <!-- URL 输入 Tab -->
              <div v-if="activeImageEditTab === 'url'">
                <input
                  v-model="imageEditUrl"
                  type="text"
                  class="w-full border border-gray-300 rounded-md px-12px py-8px text-sm focus:border-blue-500 focus:outline-none"
                  :placeholder="$t('artifactsCodeHtml.imageEditPlaceholder')"
                  @keyup.enter="confirmImageEdit"
                >
              </div>

              <!-- 文件上传 Tab -->
              <div v-else-if="activeImageEditTab === 'upload'">
                <el-upload
                  class="w-full"
                  :show-file-list="false"
                  :accept="imageAccept"
                  :before-upload="beforeImageUpload"
                  :http-request="handleImageUpload"
                  :disabled="imageUploading"
                >
                  <div class="w-full cursor-pointer border-2 border-gray-300 rounded-md border-dashed px-20px py-15px text-center transition-colors hover:border-blue-400">
                    <div v-if="imageUploading" class="flex items-center justify-center gap-8px text-blue-500">
                      <i class="i-line-md-loading-twotone-loop text-16px"></i>
                      <span class="text-sm">{{ $t('artifactsCodeHtml.imageEditUploading') }}</span>
                    </div>
                    <div v-else class="flex items-center justify-center gap-8px text-gray-500">
                      <i class="i-ri-upload-2-line text-20px"></i>
                      <span class="text-sm">{{ $t('artifactsCodeHtml.imageEditUploadText') }}</span>
                    </div>
                  </div>
                </el-upload>
              </div>

              <!-- 图标选择 Tab -->
              <div v-else-if="activeImageEditTab === 'icon'">
                <IconSelector
                  :visible="true"
                  @select="onIconSelect"
                />
              </div>

              <div class="mt-15px flex items-center justify-end gap-10px">
                <button
                  class="border border-gray-300 rounded-md px-16px py-6px text-sm text-gray-600 hover:bg-gray-50"
                  @click="closeImageEdit"
                >
                  {{ $t('artifactsCodeHtml.imageEditCancel') }}
                </button>
                <button
                  class="rounded-md bg-blue-500 px-16px py-6px text-sm text-white hover:bg-blue-600"
                  :disabled="!canConfirmEdit"
                  @click="confirmImageEdit"
                >
                  {{ $t('artifactsCodeHtml.imageEditConfirm') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </ResizablePanel>
</template>

<script setup lang="ts">
import type { UploadRawFile, UploadRequestOptions } from 'element-plus'
import { ElLoading } from 'element-plus'
import html2canvas from 'html2canvas'
import { throttle } from 'lodash'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'
import { useI18n } from 'vue-i18n'
import service from '@/common/axios'
import { image_accept } from '@/common/constant'
import {
  containsAnyPresetCDN,
  getAllScriptTags,
  getFullHtmlTemplate,
  getHeadTemplate,
} from '@/common/htmlTemplates'
import { downloadBlob } from '@/common/imageHandler'
import { notify } from '@/common/tools'
import { mixpanel } from '@/config/mixpanel'
import { useThemeChange } from '@/hooks/useThemeChange'
import { useAppStore } from '@/stores/app'
import { useChatStore } from '@/stores/chat'
import { injectChatView } from '@/views/chat-view/provide'
import IconSelector from './IconSelector.vue'
import ResizablePanel from './ResizablePanel.vue'

const props = defineProps<{
  htmlCode: string
  isSharePage: boolean
  llmStreaming: boolean
  llmGenerating: boolean
}>()

// 获取ChatView提供的方法
const chatView = injectChatView()

const visible = defineModel('visible', { type: Boolean, default: false })
const monacoRef = useTemplateRef('monacoRef')
const tab = defineModel<'code' | 'preview'>('tab', {
  default: 'code',
})
const { isDark } = useThemeChange()

const { isPc, isClient, currentArtifacts } = storeToRefs(useAppStore())
const loading = ref(false)
const deployLoading = ref(false)
const fullVisible = ref(false)
const codeSaving = ref(false) // 添加保存状态

const iframeLoaded = ref(false)
const previewFrame = ref<HTMLIFrameElement | null>(null)
const editorContent = ref(props.htmlCode || '')

// 元素选择器相关状态
const elementPickerActive = ref(false)
const mouseX = ref(0)
const mouseY = ref(0)
const currentElement = ref<HTMLElement | null>(null)
const currentElementTag = ref('')
const currentElementClasses = ref('')
const elementBounding = ref<{ width: number, height: number, left: number, top: number } | null>(null)

// 元素锁定相关状态
const elementLocked = ref(false)
const userInput = ref('')

// 文本编辑模式状态
const isEditMode = ref(false)
const EDITABLE_SELECTOR = 'h1, h2, h3, h4, h5, h6, p, li, span, div, button'
const EDIT_MODE_STYLE_ID = 'edit-mode-style' // 编辑模式样式的ID

// 图片编辑相关状态
const imageEditVisible = ref(false)
const currentImageElement = ref<HTMLImageElement | null>(null)
const imageEditUrl = ref('')
const imageUploading = ref(false)

// 图片编辑 Tab 相关状态
const activeImageEditTab = ref<'url' | 'upload' | 'icon'>('url')
const selectedIcon = ref('')

// Tab 配置
const imageEditTabs = computed(() => [
  { key: 'url' as const, label: t('artifactsCodeHtml.imageEditTabUrl') },
  { key: 'upload' as const, label: t('artifactsCodeHtml.imageEditTabUpload') },
  { key: 'icon' as const, label: t('artifactsCodeHtml.imageEditTabIcon') },
])

// 是否可以确认编辑
const canConfirmEdit = computed(() => {
  if (activeImageEditTab.value === 'url') {
    return imageEditUrl.value.trim() !== '' && !imageUploading.value
  }
  else if (activeImageEditTab.value === 'upload') {
    return imageEditUrl.value.trim() !== '' && !imageUploading.value
  }
  else if (activeImageEditTab.value === 'icon') {
    return selectedIcon.value !== ''
  }
  return false
})

// 节流版本的滚动函数
const throttledScrollToBottom = throttle((editor) => {
  if (!editor || !editor.getModel()) { return }

  const lineCount = editor.getModel().getLineCount() || 0
  if (lineCount <= 0) {
    return
  }
  // 使用revealLine方法，滚动到最后一行
  editor.revealLine(lineCount)

  // 确保光标也移动到最后
  const lastLineLength = editor.getModel().getLineLength(lineCount)
  editor.setPosition({ lineNumber: lineCount, column: lastLineLength + 1 })
}, 200)

// 监听props中的code变化，同步到编辑器
watch(
  () => props.htmlCode,
  (newVal, oldVal) => {
    if (newVal === undefined) {
      return
    }
    // 检查内容是否真的发生变化
    const contentChanged = newVal !== oldVal
    editorContent.value = newVal

    // 只在内容真正变化且处于流式传输时滚动
    if (!props.llmStreaming || !contentChanged) {
      return
    }
    nextTick(() => {
      const editor = monacoRef.value?.getEditor()
      if (!editor) {
        return
      }
      throttledScrollToBottom(editor)
    })
  },
  { immediate: true },
)

// 监听tab和fullVisible的变化，解锁元素
watch(
  () => fullVisible.value,
  () => {
    unlockElement()
    closeImageEdit()
  },
)

const panelTabs = [
  { key: 'code', label: 'Code' },
  { key: 'preview', label: 'Preview' },
]
// 监听 tab 变化处理副作用
watch(tab, (newValue) => {
  if (newValue === 'preview') {
    updatePreview()
  }
  // 当tab变化时，确保元素选择器和锁定状态被重置
  unlockElement()
  stopElementPicker()
  closeImageEdit()
  if (isEditMode.value) {
    // 如果切换tab时处于编辑模式，则退出编辑模式
    toggleEditMode()
  }
})

// 处理tab点击事件
function handleTabClick(tabKey: 'code' | 'preview') {
  // 先判断是否可以切换到预览
  if (tabKey === 'preview') {
    if (props.llmStreaming) {
      tab.value = 'code'
      notify.warning({
        title: t('tipMessage.llmStreaming'),
      })
      return
    }
    // 更新 tab 值
    tab.value = 'preview'
  }
  else if (tabKey === 'code') {
    tab.value = 'code'
  }

  // 切换预览时关闭元素选择器/解锁
  if (tabKey === 'code') { // 当切换到 'code' 标签页时
    if (elementPickerActive.value) {
      elementPickerActive.value = false
      stopElementPicker()
    }
    if (elementLocked.value) {
      unlockElement()
    }
    if (imageEditVisible.value) {
      closeImageEdit()
    }
    // 如果当前处于编辑模式，并且切换到了 'code' 标签页，则退出编辑模式
    if (isEditMode.value) {
      // toggleEditMode() // 这会移除contenteditable和样式 - 在 watch tab 中已经处理
    }
  }
}
// 切换元素选择器
function toggleElementPicker() {
  // 如果有锁定的元素，先解锁
  if (elementLocked.value) {
    unlockElement()
  }
  // 如果在编辑模式，先退出编辑模式
  if (isEditMode.value) {
    toggleEditMode()
  }
  // 关闭图片编辑弹窗
  closeImageEdit()

  elementPickerActive.value = !elementPickerActive.value

  if (elementPickerActive.value) {
    // 启动元素选择器
    startElementPicker()
  }
  else {
    // 停止元素选择器
    stopElementPicker()
  }
}

// 启动元素选择器
function startElementPicker() {
  if (!previewFrame.value || !previewFrame.value.contentDocument) { return }

  // 添加mousemove事件监听
  previewFrame.value.contentDocument.addEventListener('mousemove', handleIframeMouseMove)

  // 添加点击事件以选择元素
  previewFrame.value.contentDocument.addEventListener('click', handleIframeClick)

  // 阻止默认行为，防止链接跳转等
  previewFrame.value.contentDocument.addEventListener('mousedown', preventDefault)
  previewFrame.value.contentDocument.addEventListener('mouseup', preventDefault)

  // 添加样式到iframe中，使鼠标悬停效果
  const style = previewFrame.value.contentDocument.createElement('style')
  style.id = 'element-picker-style'
  style.textContent = `
    * {
      cursor: crosshair !important;
    }
    *:hover {
      outline: 2px dashed rgba(62, 175, 124, 0.5) !important;
    }
  `
  previewFrame.value.contentDocument.head.appendChild(style)
}

// 停止元素选择器的交互行为（这里只是停止交互行为，就是那个飘来飘去的框框）
function stopElementPicker() {
  if (!previewFrame.value || !previewFrame.value.contentDocument) { return }

  // 移除事件监听器
  previewFrame.value.contentDocument.removeEventListener('mousemove', handleIframeMouseMove)
  previewFrame.value.contentDocument.removeEventListener('click', handleIframeClick)
  previewFrame.value.contentDocument.removeEventListener('mousedown', preventDefault)
  previewFrame.value.contentDocument.removeEventListener('mouseup', preventDefault)

  // 移除样式
  const style = previewFrame.value.contentDocument.getElementById('element-picker-style')
  if (style) {
    style.remove()
  }

  // 清除状态（如果不是处于锁定状态）
  if (!elementLocked.value) {
    currentElement.value = null
    elementBounding.value = null
  }
}

// 处理iframe中的鼠标移动
function handleIframeMouseMove(e: MouseEvent) {
  // 获取iframe的位置和大小
  if (!previewFrame.value) { return }
  const rect = previewFrame.value.getBoundingClientRect()

  // 计算鼠标在页面中的位置
  mouseX.value = e.clientX + rect.left
  mouseY.value = e.clientY + rect.top

  const iframeDoc = previewFrame.value.contentDocument
  if (!iframeDoc) { return }

  // 获取鼠标下的元素
  const element = iframeDoc.elementFromPoint(e.clientX, e.clientY) as HTMLElement

  if (element) {
    currentElement.value = element
    currentElementTag.value = element.tagName.toLowerCase()
    currentElementClasses.value = element.className

    // 获取元素在iframe中的位置
    const elementRect = element.getBoundingClientRect()

    // 转换为相对于页面的位置
    elementBounding.value = {
      width: elementRect.width,
      height: elementRect.height,
      left: elementRect.left + rect.left,
      top: elementRect.top + rect.top,
    }
  }
}

// 处理iframe中的点击
function handleIframeClick(e: MouseEvent) {
  e.preventDefault()
  e.stopPropagation()

  if (currentElement.value) {
    // 锁定当前元素
    elementLocked.value = true

    // 关闭元素选择器但保留当前元素
    elementPickerActive.value = false

    // 停止选择器但不清除当前元素状态
    if (previewFrame.value && previewFrame.value.contentDocument) {
      previewFrame.value.contentDocument.removeEventListener('mousemove', handleIframeMouseMove)
      previewFrame.value.contentDocument.removeEventListener('click', handleIframeClick)
      previewFrame.value.contentDocument.removeEventListener('mousedown', preventDefault)
      previewFrame.value.contentDocument.removeEventListener('mouseup', preventDefault)

      // 移除样式
      const style = previewFrame.value.contentDocument.getElementById('element-picker-style')
      if (style) {
        style.remove()
      }
    }

    // 在元素锁定后，等待DOM更新，然后聚焦到textarea
    nextTick(() => {
      const textarea = document.querySelector('.element-locked-panel textarea')
      if (textarea) {
        (textarea as HTMLTextAreaElement).focus()
      }
    })
  }

  return false
}

// 阻止默认行为
function preventDefault(e: Event) {
  e.preventDefault()
  e.stopPropagation()
  return false
}

/**
 * 初始化预览数据
 * @param data 组件数据
 */
function initData(data: any): void {
  // 生成中时，显示code
  if (!data.code || !data.id) {
    tab.value = 'code'
  }
  // 历史记录或生成过程中获取到code或id
  else {
    if (data.code) {
      editorContent.value = data.code
    }
  }
}

/**
 * 确保HTML包含必要的CDN脚本
 * @param html HTML内容
 * @returns 处理后的HTML
 */
function ensureCDNScripts(html: string): string {
  // 检查是否已经包含预设CDN
  if (containsAnyPresetCDN(html)) {
    return html
  }

  // 检查是否有完整的HTML结构
  if (!html.includes('<!DOCTYPE html>') && !html.includes('<html')) {
    return getFullHtmlTemplate(html)
  }

  // 检查是否有head标签
  if (html.includes('<head>') || html.includes('<head ')) {
    // 在head标签结束前插入CDN脚本
    return html.replace(/<\/head>/, `${getAllScriptTags()}\n</head>`)
  }

  // 有html标签但没有head标签，在html开始标签后添加head
  if (html.includes('<html>') || html.includes('<html ')) {
    const htmlTagRegex = /<html[^>]*>/
    const match = html.match(htmlTagRegex)
    if (match) {
      const position = match.index! + match[0].length
      return `${html.slice(0, position)
      }\n${getHeadTemplate()}\n${
        html.slice(position)}`
    }
  }

  // 其他情况，在开头添加基本HTML结构
  return getFullHtmlTemplate(html)
}

/**
 * 更新iframe预览内容
 */
function updatePreview(): void {
  if (!previewFrame.value || !editorContent.value) { return }

  iframeLoaded.value = false

  // 如果元素选择器处于活动状态，则停止它
  if (elementPickerActive.value) {
    elementPickerActive.value = false
    stopElementPicker()
  }

  // 如果有锁定的元素，取消锁定
  if (elementLocked.value) {
    unlockElement()
  }

  // 关闭图片编辑弹窗
  if (imageEditVisible.value) {
    closeImageEdit()
  }

  // 如果在编辑模式，先退出编辑模式，以移除旧的样式和contenteditable
  if (isEditMode.value) {
    // 只是重置状态，实际的DOM操作会在toggleEditMode中处理
    // 这里确保在更新预览前，编辑模式相关的状态是干净的
    const iframeDoc = previewFrame.value?.contentDocument
    if (iframeDoc) {
      removeEditModeStyles(iframeDoc)
      setElementsEditable(iframeDoc, false)
    }
    isEditMode.value = false // 直接设置为false，因为预览将刷新
  }

  // 确保HTML包含必要的CDN脚本
  const htmlContent = ensureCDNScripts(editorContent.value)

  // 创建blob URL以避免跨域和worker问题
  const blob = new Blob([htmlContent], { type: 'text/html' })
  const blobUrl = URL.createObjectURL(blob)

  // 设置iframe的src为blob URL
  previewFrame.value.src = blobUrl

  // 当iframe加载完成后释放blob URL
  previewFrame.value.onload = () => {
    iframeLoaded.value = true
    URL.revokeObjectURL(blobUrl)
  }

  // 记录预览事件
  try {
    mixpanel.artifacts('preview')
  }
  catch (error) {
    console.error('Mixpanel error:', error)
  }
}
const { t } = useI18n()
const { copy } = useClipboard()

// 处理截图
async function handleScreenshot() {
  if (!previewFrame.value || !iframeLoaded.value) {
    return
  }

  try {
    ElLoading.service({
      lock: true,
      text: t('artifactsCodeHtml.screenshotGenerating'),
      background: 'rgba(0, 0, 0, 0.7)',
    })

    const iframe = previewFrame.value
    const iframeDoc = iframe.contentDocument
    if (!iframeDoc) { return }

    // 使用html2canvas生成截图
    const canvas = await html2canvas(iframeDoc.documentElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      logging: false,
    })

    // 将canvas转换为blob
    canvas.toBlob((blob) => {
      if (blob) {
        const filename = `screenshot-${new Date().toISOString().slice(0, 10)}.png`
        downloadBlob(blob, filename)
        notify.success({
          title: t('artifactsCodeHtml.screenshotSuccess'),
        })
      }
    }, 'image/png', 1.0)
  }
  catch (error) {
    console.error('截图失败:', error)
  }
  finally {
    ElLoading.service().close()
  }
}
function handleCopyCode(): void {
  copy(editorContent.value)
  notify.success({
    title: t('tipMessage.copySuccess'),
  })
}
/**
 * 下载代码为HTML文件
 */
function handleDownloadCode(): void {
  if (!editorContent.value) { return }

  // 确保HTML包含必要的CDN脚本
  const htmlWithCDN = ensureCDNScripts(editorContent.value)

  const blob = new Blob([htmlWithCDN], { type: 'text/html;charset=utf-8' })
  downloadBlob(blob, 'index.html')
}

/**
 * iframe加载完成的处理函数
 */
function handleIframeLoad(): void {
  iframeLoaded.value = true
}

// 监听可见性变化，当面板关闭时停止元素选择器
watch(
  () => visible.value,
  (newVal) => {
    if (!newVal) {
      if (elementPickerActive.value) {
        elementPickerActive.value = false
        stopElementPicker()
      }
      if (elementLocked.value) {
        unlockElement()
      }
      if (isEditMode.value) {
        toggleEditMode() // 面板关闭时退出编辑模式
      }
      if (imageEditVisible.value) {
        closeImageEdit() // 面板关闭时关闭图片编辑弹窗
      }
    }
  },
)

// 初始化，监听props.data变化
watch(
  () => props.htmlCode,
  (newVal) => {
    if (newVal) {
      initData(newVal)
    }
  },
)

// 切换文本编辑模式
function toggleEditMode() {
  if (!previewFrame.value || !previewFrame.value.contentDocument) {
    console.warn('预览框架不适用于编辑模式')
    return
  }
  // 如果元素选择器是激活的，先关闭它
  if (elementPickerActive.value) {
    toggleElementPicker()
  }

  isEditMode.value = !isEditMode.value
  const iframeDoc = previewFrame.value.contentDocument

  if (isEditMode.value) {
    // 进入编辑模式
    setElementsEditable(iframeDoc, true)
    addEditModeStyles(iframeDoc)
    addImageClickListeners(iframeDoc)
    // 移除元素选择器可能添加的事件，避免冲突
    iframeDoc.removeEventListener('mousemove', handleIframeMouseMove)
    iframeDoc.removeEventListener('click', handleIframeClick)
    iframeDoc.removeEventListener('mousedown', preventDefault)
    iframeDoc.removeEventListener('mouseup', preventDefault)
    const pickerStyle = iframeDoc.getElementById('element-picker-style')
    if (pickerStyle) { pickerStyle.remove() }
  }
  else {
    // 退出编辑模式
    setElementsEditable(iframeDoc, false)
    removeEditModeStyles(iframeDoc)
    removeImageClickListeners(iframeDoc)
  }
}

// 设置/移除可编辑元素的contenteditable属性
function setElementsEditable(doc: Document, editable: boolean) {
  const elements = doc.querySelectorAll(EDITABLE_SELECTOR)
  elements.forEach((el) => {
    if (editable) {
      el.setAttribute('contenteditable', 'true')
    }
    else {
      el.removeAttribute('contenteditable')
    }
  })
}

// 添加编辑模式的视觉提示样式
function addEditModeStyles(doc: Document) {
  removeEditModeStyles(doc) // 先移除可能存在的旧样式
  const style = doc.createElement('style')
  style.id = EDIT_MODE_STYLE_ID
  style.textContent = `
    [contenteditable="true"] {
      outline: 1px dashed #09f !important;
      cursor: text !important;
    }
    /* 图片在编辑模式下的样式 */
    img {
      cursor: pointer !important;
      transition: opacity 0.2s ease;
    }
    img:hover {
      opacity: 0.8 !important;
      outline: 2px dashed #09f !important;
    }
    /* 防止在编辑模式下触发元素选择器的hover效果 */
    *:hover {
      outline: none !important;
    }
    /* 图片hover时覆盖上面的规则 */
    img:hover {
      opacity: 0.8 !important;
      outline: 2px dashed #09f !important;
    }
  `
  doc.head.appendChild(style)
}

// 移除编辑模式的视觉提示样式
function removeEditModeStyles(doc: Document) {
  const styleElement = doc.getElementById(EDIT_MODE_STYLE_ID)
  if (styleElement) {
    styleElement.remove()
  }
}

// 为图片添加点击事件监听器
function addImageClickListeners(doc: Document) {
  const images = doc.querySelectorAll('img')
  images.forEach((img) => {
    img.addEventListener('click', handleImageClick)
  })
}

// 移除图片点击事件监听器
function removeImageClickListeners(doc: Document) {
  const images = doc.querySelectorAll('img')
  images.forEach((img) => {
    img.removeEventListener('click', handleImageClick)
  })
}

// 处理图片点击事件
function handleImageClick(e: Event) {
  e.preventDefault()
  e.stopPropagation()

  const imgElement = e.target as HTMLImageElement
  if (imgElement && imgElement.tagName === 'IMG') {
    openImageEdit(imgElement)
  }
}

// 处理保存并获取HTML
function handleSaveAndGetHtml() {
  const htmlString = getCleanedIframeHtml()
  if (!htmlString) {
    return
  }

  // 将HTML内容同步回 Monaco 编辑器
  editorContent.value = htmlString

  // 发请求保存，实际上发出去的是editorContent的内容
  handleSaveCode()
}

// 退出元素锁定状态（只有选择中了元素后，才会进入锁定状态，这里负责解锁）
function unlockElement() {
  elementLocked.value = false
  currentElement.value = null
  elementBounding.value = null
  userInput.value = ''
}

// 图片编辑相关方法
function openImageEdit(imgElement: HTMLImageElement) {
  currentImageElement.value = imgElement
  imageEditUrl.value = imgElement.src || ''
  imageEditVisible.value = true

  // 聚焦到输入框
  nextTick(() => {
    const input = document.querySelector('.image-edit-modal input') as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  })
}

function closeImageEdit() {
  imageEditVisible.value = false
  currentImageElement.value = null
  imageEditUrl.value = ''
  imageUploading.value = false
  // 重置图标选择相关状态
  activeImageEditTab.value = 'url'
  selectedIcon.value = ''
}

async function confirmImageEdit() {
  if (!currentImageElement.value) {
    return
  }

  let newSrc = ''

  if (activeImageEditTab.value === 'url' || activeImageEditTab.value === 'upload') {
    if (!imageEditUrl.value.trim()) {
      return
    }
    newSrc = imageEditUrl.value.trim()
  }
  else if (activeImageEditTab.value === 'icon') {
    if (!selectedIcon.value) {
      return
    }
    // 生成 SVG 图标的 data URL
    newSrc = await generateIconDataUrl(selectedIcon.value)
  }

  // 更新图片的src属性
  currentImageElement.value.src = newSrc

  // 同步更新编辑器内容
  syncIframeToEditor()

  // 关闭弹窗
  closeImageEdit()
}

// 图标选择回调
function onIconSelect(iconName: string) {
  selectedIcon.value = iconName
}

// 生成图标的 data URL
async function generateIconDataUrl(iconName: string): Promise<string> {
  try {
    // 使用 Iconify API 获取 SVG 内容
    const response = await fetch(`https://api.iconify.design/lucide/${iconName}.svg?color=%23374151&width=64&height=64`)
    if (response.ok) {
      const svgContent = await response.text()
      // 将 SVG 转换为 data URL
      const dataUrl = `data:image/svg+xml;base64,${btoa(svgContent)}`
      return dataUrl
    }
  }
  catch (error) {
    console.error('Error fetching icon:', error)
  }

  // 如果获取失败，返回一个默认的 SVG
  return generateDefaultIconSvg(iconName)
}

// 生成默认的 SVG 图标
function generateDefaultIconSvg(iconName: string): string {
  const svg = `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#374151" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
    <text x="12" y="16" text-anchor="middle" font-size="8" fill="#374151">${iconName.slice(0, 3)}</text>
  </svg>`
  return `data:image/svg+xml;base64,${btoa(svg)}`
}

// 图片上传相关
const imageAccept = computed(() => image_accept.join(','))

// 上传前的验证
function beforeImageUpload(rawFile: UploadRawFile) {
  const isImage = rawFile.type.startsWith('image/')
  if (!isImage) {
    notify.error({
      title: '只能上传图片文件',
    })
    return false
  }

  const isLt10M = rawFile.size / 1024 / 1024 < 10
  if (!isLt10M) {
    notify.error({
      title: '图片大小不能超过 10MB',
    })
    return false
  }

  return true
}

// 处理图片上传
async function handleImageUpload(options: UploadRequestOptions) {
  imageUploading.value = true

  try {
    const formData = new FormData()
    formData.append(options.filename, options.file, options.file.name)

    const localeLang = localStorage.getItem('localeLang') || ''
    const config = {
      headers: {
        'Jtoken': localStorage.getItem('token'),
        'Content-Type': 'multipart/form-data',
        'noninductive': true,
        'Accept-Language': localeLang.includes('en') ? 'en' : 'zh',
      },
    }

    const response = await service.post('/file/uploadFileToCloudflareR2', formData, config)

    if (response && response.data && response.data.originUrl) {
      // 上传成功，设置图片URL
      imageEditUrl.value = response.data.originUrl
      notify.success({
        title: t('artifactsCodeHtml.imageEditUploadSuccess'),
      })
    }
    else {
      throw new Error('Upload response invalid')
    }
  }
  catch (error) {
    console.error('Image upload failed:', error)
    notify.error({
      title: t('artifactsCodeHtml.imageEditUploadFailed'),
    })
  }
  finally {
    imageUploading.value = false
  }
}

// 获取清理后的iframe HTML内容
function getCleanedIframeHtml(): string | null {
  if (!previewFrame.value || !previewFrame.value.contentDocument) {
    return null
  }

  const iframeDoc = previewFrame.value.contentDocument

  // 克隆文档以进行清理，避免影响当前显示的iframe
  const clonedDoc = iframeDoc.cloneNode(true) as Document

  // 从克隆文档的所有元素中移除 contenteditable 属性
  const allElementsInClonedDoc = clonedDoc.querySelectorAll('*')
  allElementsInClonedDoc.forEach(el => el.removeAttribute('contenteditable'))

  // 移除编辑模式下的临时视觉样式 (ID: EDIT_MODE_STYLE_ID)
  const editModeStyleElement = clonedDoc.getElementById(EDIT_MODE_STYLE_ID)
  if (editModeStyleElement) {
    editModeStyleElement.remove()
  }

  // 移除由 Tailwind CDN 脚本动态注入的 <style> 标签
  const headStyles = clonedDoc.head.querySelectorAll('style')
  const tailwindCdnScriptExists = Array.from(clonedDoc.head.querySelectorAll('script')).some(
    s => s.src.includes('cdn.tailwindcss.com'),
  )

  if (tailwindCdnScriptExists) {
    headStyles.forEach((styleTag) => {
      const styleContent = styleTag.textContent || ''
      // 检查特征：Tailwind 的注入样式通常包含 "/*! tailwindcss v" 或大量 CSS 变量定义
      // 并且通常没有 ID，也不是用户在原始 HTML (editorContent) 中直接编写的。
      const isLikelyTailwindInjected = (
        styleContent.includes('/*! tailwindcss v')
        || styleContent.includes('*, ::before, ::after{--tw-border-spacing-x:0;')
      ) && !styleTag.id

      if (isLikelyTailwindInjected) {
        // 为避免误删用户在 editorContent 中定义的、恰好也符合上述特征的 style 标签，
        // 检查这个 style 标签的 outerHTML 是否存在于原始 editorContent 中。
        let isUserDefinedStyle = false
        if (editorContent.value) {
          try {
            const tempDiv = document.createElement('div')
            tempDiv.innerHTML = editorContent.value
            const originalStyles = tempDiv.querySelectorAll('head > style')
            originalStyles.forEach((originalStyle) => {
              if (originalStyle.isEqualNode(styleTag)) {
                isUserDefinedStyle = true
              }
            })
          }
          catch {
            // editorContent 可能不是完整的 HTML，解析可能失败
            // 在这种情况下，我们保守一点，假设它不是用户定义的（如果它看起来像 Tailwind 注入的）
            // 或者，如果 editorContent.value.includes(styleTag.outerHTML) 这种简单字符串比较更安全
            if (editorContent.value.includes(styleTag.outerHTML)) {
              isUserDefinedStyle = true
            }
          }
        }

        if (!isUserDefinedStyle) {
          styleTag.remove()
        }
      }
    })
  }

  // 获取完整的HTML字符串
  return `<!DOCTYPE html>\n${clonedDoc.documentElement.outerHTML}`
}

// 同步iframe内容到编辑器
function syncIframeToEditor() {
  const htmlString = getCleanedIframeHtml()
  if (htmlString) {
    editorContent.value = htmlString
  }
}
const chatStore = useChatStore()

// 处理发送按钮点击
function handleSendElement() {
  if (!currentElement.value || !userInput.value.trim()) { return }

  // 获取元素的完整HTML字符串
  const elementHtml = currentElement.value.outerHTML
  const prompt = `<artifacts-tweak>
  {
    "artifactsId": ${currentArtifacts.value?.id},
    "artifactsTitle": "${currentArtifacts.value?.title}", 
    "elementHtml": ${JSON.stringify(elementHtml)},
    "userPrompt": ${JSON.stringify(userInput.value)}
  }
  </artifacts-tweak>`
  const obj = {
    prompt,
  }
  setTimeout(() => {
    chatStore.sendMessage(obj)
  }, 500)
  // 清除状态
  unlockElement()
}

/**
 * 初始化/重置组件状态
 * 在组件收起后可以被外部调用，用于重置组件状态
 */
function initialize() {
  // 重置标签页到默认状态
  tab.value = 'code'

  // 重置全屏状态
  fullVisible.value = false

  // 重置编辑器内容
  editorContent.value = ''

  // 重置预览相关状态
  iframeLoaded.value = false

  // 重置元素选择器状态
  if (elementPickerActive.value) {
    elementPickerActive.value = false
    stopElementPicker()
  }

  // 解锁任何锁定的元素
  if (elementLocked.value) {
    unlockElement()
  }

  // 关闭图片编辑弹窗
  if (imageEditVisible.value) {
    closeImageEdit()
  }
}

// 计算锁定面板的位置样式
const lockedPanelStyle = computed(() => {
  if (!elementBounding.value || !currentElement.value) {
    return {}
  }

  // 基本位置
  const style: Record<string, string> = {
    width: '300px',
  }

  // 智能定位面板 - 不管是否全屏，都使用相同的定位逻辑
  // 获取元素在视口中的位置信息
  const elementLeft = elementBounding.value.left
  const elementRight = elementLeft + elementBounding.value.width
  const elementTop = elementBounding.value.top
  const elementBottom = elementTop + elementBounding.value.height

  const panelWidth = 300 // 面板宽度
  const panelHeight = 250 // 估计面板高度
  const margin = 10 // 与元素间的边距
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // 水平位置 - 优先尝试放在元素右侧
  if (elementRight + panelWidth + margin < viewportWidth) {
    // 放在元素右侧
    style.left = `${elementRight + margin}px`
    style.right = 'auto'
  }
  else if (elementLeft - panelWidth - margin > 0) {
    // 如果右侧放不下，尝试放在元素左侧
    style.left = 'auto'
    style.right = `${viewportWidth - elementLeft + margin}px`
  }
  else {
    // 如果左右都放不下，尝试水平居中对齐
    const leftPos = Math.max(20, Math.min(elementLeft, viewportWidth - panelWidth - 20))
    style.left = `${leftPos}px`
    style.right = 'auto'
  }

  // 垂直位置 - 优先放在元素下方
  if (elementBottom + panelHeight + margin < viewportHeight) {
    // 放在元素下方
    style.top = `${elementBottom + margin}px`
  }
  else if (elementTop - panelHeight - margin > 0) {
    // 如果下方放不下，尝试放在元素上方
    style.top = `${elementTop - panelHeight - margin}px`
  }
  else {
    // 如果上下都放不下，尽量放在可见区域
    const topPos = Math.max(20, Math.min(elementTop, viewportHeight - panelHeight - 20))
    style.top = `${topPos}px`
  }

  return style
})

const monacoOptions = computed(() => ({
  readOnly: props.llmStreaming,
  language: props.llmStreaming ? 'plaintext' : 'html',
}))

async function handleSaveCode() {
  if (!currentArtifacts.value || codeSaving.value) {
    return
  }

  codeSaving.value = true

  try {
    const artifactId = currentArtifacts.value.id
    let title = ''
    let description = ''

    // 处理两种数据结构
    if (Object.hasOwn(currentArtifacts.value, 'html')) {
      // 格式 1: 历史记录获取回来的 { id, title, description, html }
      title = currentArtifacts.value.title
      description = currentArtifacts.value.description || ''
    }
    else if (Object.hasOwn(currentArtifacts.value as any, 'artifacts')
      && typeof (currentArtifacts.value as any).artifacts === 'string') {
      // 格式 2: sse回来的 { id, artifacts: "{\"title\":\"...\",\"description\":\"...\",\"html\":\"...\"}" }
      try {
        const parsedArtifacts = JSON.parse((currentArtifacts.value as any).artifacts)
        title = parsedArtifacts.title || ''
        description = parsedArtifacts.description || ''
      }
      catch (e) {
        console.error('Failed to parse artifacts JSON string:', e)
        notify.error({ title: t('tipMessage.saveFailed'), message: t('artifactsCodeHtml.parseError') })
        return
      }
    }
    else {
      console.error('Unknown currentArtifacts format:', currentArtifacts.value)
      notify.error({ title: t('tipMessage.saveFailed'), message: t('artifactsCodeHtml.unknownFormat') })
      return
    }

    const jsonObj = {
      title,
      description,
      html: editorContent.value,
    }

    const payload = {
      id: artifactId,
      jsonObj: JSON.stringify(jsonObj),
    }

    await service.post('/gpt/updArtifacts', payload)

    // 保存成功后，临时修补section中的数据
    // 由于修改成功后，只是后端数据更新了，但是前端数据没有更新，所以使用工具函数修补sections对话数据
    // 等切换对话/刷新页面，就是从历史记录接口获取数据，此时sections中的数据已经更新了
    if (chatView && artifactId) {
      chatView.updateArtifactHtml(artifactId, editorContent.value)
    }

    notify.success({ title: t('tipMessage.saveSuccess') })
  }
  catch (error: any) {
    console.log('error: ', error)
  }
  finally {
    codeSaving.value = false
  }
}

defineExpose({
  initialize,
})
</script>

<style lang="scss" scoped>
:deep(.monaco-editor) {
  border-radius: 4px;
  width: 100% !important;
  height: calc(100vh - 200px) !important;
}

:deep(.overflow-guard) {
  border-radius: 4px;
}

code,
kbd,
samp,
pre {
  white-space: pre-wrap;
}

.send_btn {
  border: 1px solid #0000 !important;

  &.is-disabled {
    background-color: var(--send-button-disabled-bg);
  }
}

.drwan_btn {
  @apply size-36px flex-c cursor-pointer border border-[var(--artifacts-code-border)] rounded-8px bg-[var(--artifacts-code-bg)] text-#727272;
}
</style>
